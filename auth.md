# Authentication and Authorization Flow Analysis

## Executive Summary

This application implements a comprehensive authentication and authorization system based on **OpenID Connect (OIDC)** with **Keycloak** as the identity provider. The system uses **session-based authentication** with secure HTTP cookies, JWT tokens for access control, and a policy-based authorization framework. The architecture supports both user authentication and specialized authentication mechanisms for different services.

## 1. Authentication Mechanism Analysis

### 1.1 Core Authentication Flow

The application uses **OpenID Connect Authorization Code Flow** with Keycloak as the identity provider:

1. **Login Initiation**: Frontend calls `/api/v1/session/login/init`
2. **OAuth2 Redirect**: User is redirected to Keycloak for authentication
3. **Authorization Code**: Keycloak redirects back with authorization code
4. **Token Exchange**: Backend exchanges code for tokens at `/api/v1/session/login/conclude`
5. **Session Creation**: Backend creates internal session and sets secure cookie

### 1.2 Token Types and Storage

**Three types of tokens are managed:**

1. **ID Token (JWT)**: Contains user identity information
   - Issuer, subject, email, given_name, family_name
   - Nonce for replay protection
   - Group membership information

2. **Access Token (JWT)**: Contains authorization information
   - Subject, issuer, expiration times
   - Roles (`qw_roles`) and group membership (`qw_groups`)
   - Nonce for session correlation

3. **Refresh Token**: Used for token renewal
   - Stored securely in database
   - Used to refresh expired access tokens

**Storage Locations:**
- **Backend Database**: All tokens stored in `session` table
- **Frontend Cookie**: Only session token (not JWT) stored as HTTP-only cookie
- **Frontend Memory**: User information cached in Angular services
- **No localStorage/sessionStorage**: No sensitive tokens stored client-side

### 1.3 Session Token Format

The session token is **not a JWT** but a custom format:
```
session_uuid + checksum
```

- **Session UUID**: 32-character UUID without hyphens
- **Checksum**: 2-character hex checksum for integrity verification
- **Salt**: Configurable salt value for checksum computation
- **Purpose**: Prevents session token guessing attacks

### 1.4 Token Validity and Refresh

**Access Token Expiration:**
- Default expiration from Keycloak configuration
- Can be overridden via `overwrite_access_expiration` setting
- Automatic refresh when expired

**Refresh Token Expiration:**
- Configured in Keycloak
- Longer-lived than access tokens
- Used for seamless token renewal

**Session Expiration:**
- Tracked in database with `ts_expiration_access` and `ts_expiration_refresh`
- Sessions can be manually invalidated via `was_logged_out` flag

## 2. Authentication Persistence and Verification

### 2.1 Cross-Session Persistence

**Session Persistence Mechanism:**
- **HTTP-Only Cookie**: Session token stored in secure cookie named `session`
- **Database Storage**: Complete session data stored in PostgreSQL
- **No Client Storage**: No authentication data in localStorage/sessionStorage

**Session Validation Process:**
```python
def find_and_validate_session(self, session_token: str) -> ValidatedSessionResult | None:
    session_uuid = self.gateway.read_session_token(session_token)  # Verify checksum
    if session_uuid is None:
        return None

    # Database lookup and validation
    session = self.find_session_by_uuid(session_uuid)
    if session is None or session.was_logged_out:
        return None

    # Check expiration and refresh if needed
    if self.is_access_token_expired(session):
        if not self.refresh_access_token(session):
            return None

    return ValidatedSessionResult(session_uuid, session.token_access)
```

### 2.2 Backend Authentication Verification

**For Falcon (Main API) Requests:**
```python
def get_access_token_and_verify_from_cookies(
    session_service: SessionService, cookies: Dict[str, str]
) -> AuthContextData:
    session_token = cookies.get("session")
    if session_token is None:
        raise falcon.HTTPUnauthorized()

    result = session_service.find_and_validate_session(session_token)
    if result is None:
        raise falcon.HTTPUnauthorized()

    valid_token = session_service.policy.inspect_access_token(result.access_token)
    if valid_token is None:
        raise falcon.HTTPUnauthorized()

    return AuthContextData(result.session_uuid, valid_token)
```

**For FastAPI (Agent Service) Requests:**
```python
async def validate_request(self, request: Request) -> AuthContext:
    session_token = request.cookies.get("session")
    if not session_token:
        raise HTTPException(status_code=401, detail="Session cookie required")

    # Validates by calling main Falcon service
    auth_context = await self.session_validator.validate_session(session_token)
    if not auth_context:
        raise HTTPException(status_code=401, detail="Invalid or expired session")

    return auth_context
```

### 2.3 Authentication Middleware and Decorators

**Base Authentication Classes:**
- `AuthOpenApiOpJsonOut`: JSON endpoints requiring authentication
- `AuthOpenApiOpJsonInJsonOut`: JSON input/output endpoints
- `AuthOpenApiOpBinaryOut`: Binary response endpoints
- `AuthOpenApiOpMultipartInJsonOut`: File upload endpoints

**Policy Evaluation Pattern:**
```python
def on_request_after_auth(self, auth_ctx: AuthContext, ...):
    auth_ctx.eval_policy(AllowAllSessions(require_membership=True))
    # Business logic here
```

**Context Manager Pattern:**
```python
with AuthContext(auth_ctx_data, self.logger) as auth_ctx:
    auth_ctx.eval_policy(policy)
    return self.handle_request(auth_ctx, ...)
```

### 2.4 Endpoint Authentication Requirements

**Public Endpoints (No Authentication):**
- `/api/health` - Health check
- `/api/openapi.json` - API specification
- `/api/v1/session/login/init` - Login initiation
- `/api/v1/session/login/conclude` - Login completion

**Authenticated Endpoints (Session Required):**
- All `/api/v1/*` endpoints except login/logout
- File upload/download endpoints
- User management endpoints
- Business logic endpoints

**Special Authentication:**
- **WOPI Endpoints**: Use JWT tokens for Office integration
- **Agent Service**: Uses session validation via HTTP calls to main service

## 3. Frontend Authentication State

### 3.1 User Information Storage

**AuthService State Management:**
```typescript
export class AuthService {
  private currentUserSubject = new BehaviorSubject<QwWhoamiView | null>(null)
  public currentUser$ = this.currentUserSubject.asObservable()

  private isDeveloperSubject = new BehaviorSubject<boolean>(this.getStoredDevStatus())
  public isDeveloper$ = this.isDeveloperSubject.asObservable()
}
```

**User Data Available to Frontend:**
- **User Identity**: ID, given_name, family_name, email
- **Tenant Information**: tenant_id, tenant roles
- **Developer Status**: Stored in localStorage (non-sensitive)
- **Session Status**: Derived from API calls, not stored

### 3.2 Authentication State Detection

**Session Validation:**
```typescript
public verifySessionOrInitiateLogin(): Observable<boolean> {
  return this.sessionServiceAPIWrapper.getSessionInfo().pipe(
    switchMap(() => this.userService.getUserWhoami()),
    tap((user) => {
      this.currentUserSubject.next(user)
    }),
    map(() => true),
    catchError((error: HttpErrorResponse) => {
      return this.handleAuthError(error)
    })
  )
}
```

**Automatic Session Monitoring:**
```typescript
interval(30000).pipe(
  takeUntilDestroyed(),
  switchMap(() => this.verifySessionOrInitiateLogin())
).subscribe()
```

### 3.3 Route Protection

**Authentication Guard:**
```typescript
export const hasValidSession: CanActivateFn = () =>
  inject(AuthService).verifySessionOrInitiateLogin()
```

**Developer Guard:**
```typescript
export const DevGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService)
  if (authService.isDeveloper()) {
    return true
  } else {
    return router.navigateByUrl(AppRoutes.fullPath("comingSoon"))
  }
}
```

### 3.4 User Interface Integration

**Header Component User Display:**
```typescript
vm$: Observable<ViewModel> = combineLatest({
  currentUser: this.authService.currentUser$,
  // ... other observables
}).pipe(
  map(({ currentUser, ... }) => ({
    currentUser,
    // ... other properties
  }))
)
```

**Available User Data in UI:**
- Full name (given_name + family_name)
- Email address
- Tenant information
- Developer status toggle
- Online/offline status

## 4. Backend Authentication Flow

### 4.1 Request Authentication Headers

**Cookie-Based Authentication:**
- **Header**: `Cookie: session=<session_token_with_checksum>`
- **No Authorization Header**: System doesn't use Bearer tokens in headers
- **CSRF Protection**: Commented out but framework exists

**Request Flow:**
1. Browser automatically sends session cookie
2. Backend extracts cookie value
3. Session token validated and parsed
4. Database lookup for session data
5. JWT access token validated
6. User context established

### 4.2 Backend User Context

**AuthContext Data Structure:**
```python
@dataclass
class AuthContextData:
    session_uuid: str
    tkn: ValidAccessToken

class AuthContext:
    def __init__(self, data: AuthContextData, logger: Logger):
        self.__data = data
        self.__submitted_policy: AuthPolicy | None = None

    @property
    def session_uuid(self) -> str:
        return self.__data.session_uuid

    @property
    def tkn(self) -> ValidAccessToken:
        return self.__data.tkn
```

**Available User Information in Backend:**
- **Identity**: issuer, subject, email, given_name, family_name
- **Authorization**: roles, group membership, tenant_id
- **Session**: session_uuid, expiration times
- **Tokens**: access_token, refresh_token (stored securely)

### 4.3 Policy-Based Authorization

**Policy Evaluation:**
```python
def eval_policy(self, policy: AuthPolicy) -> None:
    if not policy.eval(self.tkn, self.logger):
        raise falcon.HTTPForbidden(
            title="Forbidden",
            description="You don't have permission to access this resource"
        )
```

**Common Policies:**
- `AllowAllSessions(require_membership=True)`: Requires valid session and tenant membership
- `AllowAllSessions(require_membership=False)`: Requires only valid session
- `AllowIf(condition)`: Custom conditional access

### 4.4 Database Integration

**Session Storage Schema:**
```sql
CREATE TABLE session (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR NOT NULL UNIQUE,
    authentication_id INTEGER NOT NULL,
    issuer VARCHAR NOT NULL,
    subject VARCHAR NOT NULL,
    ts_expiration_access TIMESTAMP NOT NULL,
    ts_expiration_refresh TIMESTAMP NOT NULL,
    token_id VARCHAR NOT NULL,
    token_access VARCHAR NOT NULL,
    token_refresh VARCHAR NOT NULL,
    was_logged_out BOOLEAN NOT NULL,
    ts_created TIMESTAMP NOT NULL,
    ts_updated TIMESTAMP NOT NULL
);
```

**User Data Integration:**
```sql
CREATE TABLE "user" (
    id SERIAL PRIMARY KEY,
    issuer VARCHAR NOT NULL,
    subject VARCHAR NOT NULL,
    given_name VARCHAR NOT NULL,
    family_name VARCHAR NOT NULL,
    email VARCHAR NOT NULL,
    tenant_id INTEGER,
    ts_created TIMESTAMP NOT NULL,
    ts_updated TIMESTAMP NOT NULL,
    CONSTRAINT issuer_and_subject UNIQUE (issuer, subject)
);
```

## 5. Machine-to-Machine Communications

### 5.1 Current State

**No Dedicated M2M Authentication**: The current system does not implement dedicated machine-to-machine authentication mechanisms. All services currently rely on the user session-based authentication flow.

**Service-to-Service Communication:**
- **Agent Service → Main Service**: Uses session validation via HTTP calls
- **MCP Server**: Uses internal token for authentication
- **WOPI Integration**: Uses JWT tokens tied to user sessions

### 5.2 Existing M2M-Like Mechanisms

**1. Agent Service Session Validation:**
```python
# Agent service validates sessions by calling main service
async with httpx.AsyncClient() as client:
    response = await client.get(
        f"{self.config.runtime_service_url}/api/v1/session",
        cookies={"session": session_token}
    )
```

**2. MCP Server Internal Token:**
```yaml
mono_mcp_server:
  internal_token: "internal-mcp-token-dev"
```

**3. WOPI JWT Tokens:**
```python
def issue_encoded_access_token(self, file_resource_revision_id: int, session_token: str) -> str:
    token_payload = WopiAccessTokenPayload(
        file_resource_revision_id=file_resource_revision_id,
        session_token=session_token,
        utc_expiration_ts=int(expiration.timestamp())
    )
    return jwt.encode({"alg": self.settings.jws_alg}, token_payload.model_dump(), self.key_data)
```

### 5.3 Recommended M2M Authentication Architecture

Based on the current architecture principles and design choices, I recommend implementing the following M2M authentication system:

**1. Service Account Tokens (Recommended Approach):**

```python
class ServiceAccountToken(BaseModel):
    service_name: str
    issuer: str = "qw-internal"
    subject: str  # service identifier
    scopes: List[str]  # permitted operations
    expiration: datetime

class ServiceAccountAuthenticationService:
    def __init__(self, settings: ServiceAccountSettings, lf: LogFactory):
        self.settings = settings
        self.key_data = jwk.OctKey.import_key(settings.service_account_key)
        self.logger = lf.get_logger(__name__)

    def issue_service_token(self, service_name: str, scopes: List[str]) -> str:
        """Issue JWT token for service-to-service communication"""
        expiration = datetime.utcnow() + self.settings.token_expiration
        payload = ServiceAccountToken(
            service_name=service_name,
            subject=f"service:{service_name}",
            scopes=scopes,
            expiration=expiration
        )
        return jwt.encode({"alg": "HS256"}, payload.model_dump(), self.key_data)

    def validate_service_token(self, token: str) -> ServiceAccountToken | None:
        """Validate service account token"""
        try:
            decoded = jwt.decode(token, self.key_data)
            return ServiceAccountToken(**decoded.claims)
        except (JoseError, ValidationError):
            return None
```

**2. Configuration Integration:**

```yaml
mono_pfoertner:
  service_auth_settings:
    service_account_key: ${OVERWRITE}
    token_expiration_hours: 24
    allowed_services:
      - name: "agent-service"
        scopes: ["session:validate", "user:read"]
      - name: "worker-service"
        scopes: ["file:process", "notification:send"]
      - name: "mcp-server"
        scopes: ["api:read", "api:write"]
```

**3. Authentication Middleware Extension:**

```python
class ServiceAccountAuthContext:
    def __init__(self, service_name: str, scopes: List[str]):
        self.service_name = service_name
        self.scopes = scopes
        self.is_service_account = True

def get_auth_context_from_request(request) -> AuthContext | ServiceAccountAuthContext:
    # Check for service account token first
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header[7:]
        service_token = service_auth.validate_service_token(token)
        if service_token:
            return ServiceAccountAuthContext(service_token.service_name, service_token.scopes)

    # Fall back to session-based authentication
    return get_access_token_and_verify_from_cookies(session_service, request.cookies)
```

**4. Policy Integration:**

```python
class AllowServiceAccount(AuthPolicy):
    def __init__(self, required_scopes: List[str]):
        self.required_scopes = required_scopes

    def eval(self, auth_ctx: AuthContext | ServiceAccountAuthContext, logger: Logger) -> bool:
        if isinstance(auth_ctx, ServiceAccountAuthContext):
            return all(scope in auth_ctx.scopes for scope in self.required_scopes)
        return False  # Regular user sessions not allowed

class AllowUserOrService(AuthPolicy):
    def __init__(self, user_policy: AuthPolicy, service_scopes: List[str]):
        self.user_policy = user_policy
        self.service_scopes = service_scopes

    def eval(self, auth_ctx: AuthContext | ServiceAccountAuthContext, logger: Logger) -> bool:
        if isinstance(auth_ctx, ServiceAccountAuthContext):
            return all(scope in auth_ctx.scopes for scope in self.service_scopes)
        else:
            return self.user_policy.eval(auth_ctx.tkn, logger)
```

**Benefits of This Approach:**
- **Consistent with Architecture**: Uses JWT tokens and policy-based authorization
- **Secure**: Separate keys and scopes for different services
- **Auditable**: Clear service identification and scope tracking
- **Flexible**: Can be extended for different service types
- **Compatible**: Works alongside existing user authentication

**Implementation Priority:**
1. Implement service account token generation and validation
2. Extend authentication middleware to support both user and service tokens
3. Update policy framework to handle service accounts
4. Migrate existing internal services (Agent Service, MCP Server) to use service accounts
5. Add configuration and deployment support

This approach maintains the existing session-based user authentication while adding proper machine-to-machine authentication that aligns with the current architectural patterns and security principles.

---

## Conclusion

The application implements a robust, enterprise-grade authentication and authorization system with the following key characteristics:

- **Security-First Design**: HTTP-only cookies, JWT validation, checksum verification
- **Scalable Architecture**: Policy-based authorization, modular service design
- **User Experience**: Automatic session monitoring, seamless token refresh
- **Enterprise Integration**: Keycloak OIDC, tenant-based access control
- **Development-Friendly**: Clear separation of concerns, comprehensive logging

The system successfully balances security, usability, and maintainability while providing a solid foundation for both user authentication and machine-to-machine communication requirements.

---

## Service Account Authentication Implementation

### Implementation Status

The service account authentication system described in section 5.3 has been **fully implemented** and is now operational. This section documents the actual implementation details.

### Core Components Implemented

**1. ServiceAccountAuthenticationService**
- JWT token generation and validation
- Service name and scope verification
- Configurable token expiration
- Integration with existing authentication framework

**2. ServiceAccountAuthContext**
- Authentication context for service accounts
- Scope and service name tracking
- Integration with unified AuthContext

**3. ServiceAccountAuthMiddleware**
- Falcon middleware for Bearer token authentication
- Thread-local context management
- Automatic fallback to session authentication

**4. Thread-Local Context Management**
- Request-scoped service account context storage
- Thread-safe context isolation
- Automatic cleanup after request processing

### Configuration

The service account authentication is configured in `mono_pfoertner.service_auth_settings`:

```yaml
mono_pfoertner:
  service_auth_settings:
    service_account_key: "your-secret-key-here"
    token_expiration_hours: 24
    allowed_services:
      - name: "mcp-server"
        scopes: ["api:read", "api:write"]
```

### Authentication Flow

**1. Token Generation:**
- Tokens can be generated using the CLI utility
- MCP server automatically generates tokens at startup
- Tokens are JWT format with service name and scopes

**2. Request Authentication:**
- Services include Bearer tokens in Authorization header
- Middleware validates tokens and stores context
- Falls back to session authentication if no Bearer token

**3. Policy Evaluation:**
- Unified AuthContext supports both user and service authentication
- Service account policies evaluate scopes and service names
- Backward compatibility with existing user policies

### MCP Server Integration

The MCP server now uses service account authentication:

```yaml
mono_mcp_server:
  enabled: true
  service_account_token: null  # Auto-generated at startup
  api_base_url: "http://qw-mono-dev-runtime:8000"
```

**Features:**
- Automatic token generation if not provided
- Service account aware HTTP client
- OpenAPI tools with Bearer token authentication
- Graceful fallback to session authentication

### CLI Tools

**Generate Service Account Tokens:**

```bash
python -m qw_pfoertner.cli.generate_service_token \
  --qw-mono-config dev_data/app.yaml \
  --service-name mcp-server
```

**Test Service Account Authentication:**

```bash
# Generate token
TOKEN=$(python -m qw_pfoertner.cli.generate_service_token \
  --qw-mono-config dev_data/app.yaml \
  --service-name mcp-server)

# Test API call
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/user/whoami
```

### Security Features

**1. Token Security:**
- JWT tokens with HMAC-SHA256 signing
- Configurable expiration (default 24 hours)
- Service-specific scope limitations
- Secure key-based validation

**2. Service Validation:**
- Only pre-configured services can obtain tokens
- Service name validation during token generation
- Scope enforcement during policy evaluation

**3. Audit and Logging:**
- All service account activities are logged
- Service name included in audit trails
- Token validation failures logged

**4. Thread Safety:**
- Thread-local context storage
- Request isolation between concurrent requests
- Automatic context cleanup

### Policy Framework Integration

**New Policy Types:**
- `AllowServiceAccount`: Service account only policies
- `AllowUserOrService`: Hybrid policies for both users and services
- Backward compatible with existing `AllowAllSessions` policies

**Policy Evaluation:**
```python
# Service account policy evaluation
if auth_ctx.is_service_account:
    service_name = auth_ctx.service_name
    # Evaluate service-specific policies
else:
    user_token = auth_ctx.tkn
    # Evaluate user-specific policies
```

### Architecture Benefits

**1. Unified Authentication:**
- Single AuthContext interface for both user and service authentication
- Consistent policy evaluation framework
- Seamless integration with existing codebase

**2. Security:**
- Proper separation between user and service authentication
- Scope-based access control for services
- Secure token storage and validation

**3. Maintainability:**
- Clean separation of concerns
- Minimal changes to existing controllers
- Backward compatibility with session authentication

**4. Scalability:**
- Thread-safe context management
- Efficient middleware-based authentication
- Support for multiple service types

This implementation successfully provides secure, scalable machine-to-machine authentication while maintaining full compatibility with the existing user authentication system.
