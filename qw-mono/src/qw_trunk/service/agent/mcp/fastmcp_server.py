"""Clean FastMCP-based MCP Server implementation."""
import async<PERSON>
import httpx
import yaml
from typing import Any, Optional, Dict
from pathlib import Path

from fastmcp import FastMCP
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.mcp.models import MCPServerConfig


class FastMCPServerService:
    """Clean FastMCP-based MCP Server service that exposes internal APIs as MCP tools."""

    def __init__(self, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY):
        self.config = config
        self.lf = lf
        self.logger = lf.get_logger(__name__)

        # FastMCP server instance (will be created from OpenAPI spec or basic server)
        self.server = FastMCP(name="QW Internal API Server")

        # Create HTTP client for internal API calls
        headers = {}

        self.logger.info("MCP server uses session-based auth")

        self.api_client = httpx.AsyncClient(
            base_url=config.api_base_url,
            timeout=30.0,
            headers=headers
        )

        # Session token for authentication
        self.current_session_token: Optional[str] = None

        # Initialize tools
        self._setup_tools()

    def _extract_session_token_from_request(self, ctx: Any) -> Optional[str]:
        """Extract session token from HTTP request headers (pass-through authentication)."""
        try:
            # Get HTTP request from context
            request = ctx.get_http_request()
            if request:
                # Check for session token in headers (from MCP client)
                session_token = request.headers.get("X-Session-Token")
                if session_token:
                    self.logger.info(f"Session token extracted from MCP request headers: {session_token[:10]}...")
                    return str(session_token)

                # Also check for session token in Authorization header (alternative format)
                auth_header = request.headers.get("Authorization")
                if auth_header and auth_header.startswith("Bearer "):
                    token = auth_header[7:]  # Remove "Bearer " prefix
                    # Check if this looks like a session token
                    if len(token) > 20:  # Session tokens are typically longer
                        self.logger.info(f"Session token extracted from Authorization header: {token[:10]}...")
                        return str(token)

                self.logger.info("No session token found in MCP request headers")
            return None
        except Exception as e:
            self.logger.info(f"Could not extract session token from MCP request: {e}")
            return None



    def _setup_tools(self) -> None:
        """Setup MCP tools."""
        try:
            # Only add OpenAPI auto-generated tools
            if self.config.enable_auto_generated_tools:
                self._setup_openapi_tools()

            self.logger.info("MCP tools setup completed successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup MCP tools: {e}")
            raise

    def set_session_token(self, session_token: str) -> None:
        """Set session token for API authentication."""
        self.current_session_token = session_token
        self.logger.info(f"Session token set for MCP server: {session_token[:10]}...")

        # Update HTTP client headers with session cookie
        self.api_client.cookies.set("session", session_token)

    def _create_session_aware_http_client(self) -> httpx.AsyncClient:
        """Create HTTP client that uses session tokens."""

        class ServiceAccountAwareTransport(httpx.AsyncHTTPTransport):
            def __init__(self, mcp_server_instance: Any):
                super().__init__()
                self.mcp_server = mcp_server_instance

            async def handle_async_request(self, request: Any) -> Any:
                # Priority 1: Try to use session token from MCP request (pass-through authentication)
                session_token = None
                try:
                    from fastmcp.server.dependencies import get_context
                    ctx = get_context()
                    session_token = self.mcp_server._extract_session_token_from_request(ctx)
                except Exception as e:
                    self.mcp_server.logger.info(f"Could not extract session token from MCP request: {e}")

                if session_token:
                    # Use session-based authentication (tenant-aware)
                    # Remove any existing Authorization header to avoid conflicts

                    request.headers["Cookie"] = f"session={str(session_token)}"
                    self.mcp_server.logger.info(f"Using session token authentication for API request: {str(session_token)[:10]}...")

                else:
                    # No authentication available
                    self.mcp_server.logger.warning("No authentication available for API request")

                return await super().handle_async_request(request)

        headers = {}

        return httpx.AsyncClient(
            base_url=self.config.api_base_url,
            timeout=30.0,
            headers=headers,
            transport=ServiceAccountAwareTransport(self)
        )

    def _setup_openapi_tools(self) -> None:
        """Setup tools from OpenAPI specification using FastMCP."""
        try:
            # Load OpenAPI spec
            spec_path = Path(self.config.openapi_spec_path)
            if not spec_path.is_absolute():
                # Try to find the spec relative to project root
                project_root = Path(__file__).parents[5]  # Go up to qw-mono root
                spec_path = project_root / self.config.openapi_spec_path

            if not spec_path.exists():
                self.logger.warning(f"OpenAPI spec not found at {spec_path}, skipping auto-generated tools")
                return

            # Load the OpenAPI spec as a dictionary
            with open(spec_path, 'r') as f:
                openapi_spec: Dict[str, Any] = yaml.safe_load(f)

            # Create session token aware HTTP client
            session_token_client = self._create_session_aware_http_client()

            # Replace the basic server with OpenAPI-generated server using session token client
            self.server = FastMCP.from_openapi(
                openapi_spec=openapi_spec,
                client=session_token_client,
                name="QW Internal API Server"
            )

            # Update our reference to the session account client
            # Note: We'll close the old client later in cleanup()
            self.api_client = session_token_client

            self.logger.info("OpenAPI tools auto-generated")

        except Exception as e:
            self.logger.error(f"Failed to setup OpenAPI tools: {e}")
            # Keep the basic server that was already created
            self.logger.warning("Continuing with basic MCP server without auto-generated tools")

    def run_server(self) -> None:
        """Run the MCP server."""
        if not self.config.enabled:
            self.logger.error("MCP server is disabled in configuration")
            return

        if not self.server:
            self.logger.error("MCP server not initialized")
            return

        self.logger.info(f"Starting FastMCP server on {self.config.host}:{self.config.port}")

        try:
            # Run the FastMCP server with the configured transport
            # For streamable-http, serve at root path for MCP Inspector compatibility
            self.server.run(
                transport=self.config.transport,
                host=self.config.host,
                port=self.config.port,
                path="/" if self.config.transport == "streamable-http" else "/mcp"
            )
        except Exception as e:
            self.logger.error(f"Failed to run FastMCP server: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup resources."""
        await self.api_client.aclose()

    @classmethod
    def from_config(cls, config: MCPServerConfig, lf: LogFactory = NO_LOG_FACTORY) -> "FastMCPServerService":
        """Create FastMCP server service from configuration."""
        return cls(config, lf)
