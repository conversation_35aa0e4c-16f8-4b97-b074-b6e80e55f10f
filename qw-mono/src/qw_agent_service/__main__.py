"""Main entry point for the FastAPI Agent Service."""
import argparse
import async<PERSON>
from pathlib import Path

from qw_config.loader import load_config
from qw_log.factory import QwLogFactory
from qw_agent_service.config_models import QwAgentServiceMonoConfig
from qw_agent_service.server import AgentServiceA<PERSON>


def main() -> None:
    """Main entry point for the agent service process."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="FastAPI Agent Service for AI processing")
    parser.add_argument("--qw-mono-config", type=Path, required=True, help="Path to configuration YAML")
    parser.add_argument("--qw-mono-overwrite-config", type=Path, default=None, help="Path to configuration overrides")
    args = parser.parse_args()

    # Load the agent service configuration including logging settings
    agent_mono_cfg = load_config(QwAgentServiceMonoConfig, args.qw_mono_config, args.qw_mono_overwrite_config)

    # Initialize log factory with proper configuration
    lf = QwLogFactory("1.0.0", "qw_agent_service")
    lf.init_logs(agent_mono_cfg.logging)
    logger = lf.get_logger(__name__)

    # Initialize agent service app
    app = AgentServiceApp.from_config(args.qw_mono_config, args.qw_mono_overwrite_config, lf)

    # Start the FastAPI server
    logger.info(f"Loaded app with configuration from {args.qw_mono_config}")
    app.run()


if __name__ == "__main__":
    main()
